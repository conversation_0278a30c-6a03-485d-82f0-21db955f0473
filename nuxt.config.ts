import tailwindcss from '@tailwindcss/vite';

// eslint-disable-next-line no-undef
export default defineNuxtConfig({
    modules: ['@nuxt/eslint', '@nuxt/icon'],
    devtools: { enabled: true },
    css: ['~/assets/css/app.css'],
    compatibilityDate: '2025-07-15',
    vite: {
        plugins: [
            tailwindcss(),
        ],
    },
    eslint: {
        config: {
            stylistic: {
                semi: true,
                indent: 4,
            },
        },
    },
});
